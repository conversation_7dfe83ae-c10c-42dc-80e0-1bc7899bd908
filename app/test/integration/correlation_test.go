//go:build integration

// Integration tests for signal correlation functionality
// Tests signal correlation ID creation, validation, and relationships
package integration

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/information-sharing-networks/signalsd/app/internal/database"
)

// TestCorrelationEnvironment sets up a test environment for correlation ID testing
// Creates:
// - An ISN with a signal type
// - A first signal
// - A second signal that is correlated with the first signal
func TestCorrelationEnvironment(t *testing.T) {
	ctx := context.Background()
	testDB := setupTestDatabase(t, ctx)
	queries := database.New(testDB)

	// Create test data
	t.Log("Creating test environment for correlation ID testing...")

	// Create owner account and ISN
	ownerAccount := createTestAccount(t, ctx, queries, "owner", "user", "<EMAIL>")
	testISN := createTestISN(t, ctx, queries, "correlation-test-isn", "Correlation Test ISN", ownerAccount.ID, "private")

	// Create signal type for testing
	signalType := createTestSignalType(t, ctx, queries, testISN.ID, "Correlation Test Signal", "1.0.0")

	t.Logf("✅ Test environment created:")
	t.Logf("   - Owner Account ID: %s", ownerAccount.ID)
	t.Logf("   - ISN: %s (ID: %s)", testISN.Slug, testISN.ID)
	t.Logf("   - Signal Type: %s/%s (ID: %s)", signalType.Slug, signalType.SemVer, signalType.ID)

	// Create first signal (will be the target of correlation)
	t.Log("Creating first signal...")
	firstSignalID, err := queries.CreateSignal(ctx, database.CreateSignalParams{
		AccountID:      ownerAccount.ID,
		LocalRef:       "correlation-test-signal-001",
		SignalTypeSlug: signalType.Slug,
		SemVer:         signalType.SemVer,
	})
	if err != nil {
		t.Fatalf("Failed to create first signal: %v", err)
	}
	t.Logf("✅ First signal created with ID: %s", firstSignalID)

	// Create second signal that correlates with the first signal
	t.Log("Creating second signal correlated with the first...")
	secondSignalID, err := queries.CreateOrUpdateSignalWithCorrelationID(ctx, database.CreateOrUpdateSignalWithCorrelationIDParams{
		AccountID:      ownerAccount.ID,
		LocalRef:       "correlation-test-signal-002",
		CorrelationID:  firstSignalID,
		SignalTypeSlug: signalType.Slug,
		SemVer:         signalType.SemVer,
	})
	if err != nil {
		t.Fatalf("Failed to create second signal with correlation: %v", err)
	}
	t.Logf("✅ Second signal created with ID: %s, correlated to: %s", secondSignalID, firstSignalID)

	// Verify the correlation relationship
	t.Log("Verifying correlation relationship...")

	// Get the second signal and verify its correlation_id
	secondSignal, err := queries.GetSignalByAccountAndLocalRef(ctx, database.GetSignalByAccountAndLocalRefParams{
		AccountID: ownerAccount.ID,
		Slug:      signalType.Slug,
		SemVer:    signalType.SemVer,
		LocalRef:  "correlation-test-signal-002",
	})
	if err != nil {
		t.Fatalf("Failed to get second signal: %v", err)
	}

	if secondSignal.CorrelationID != firstSignalID {
		t.Errorf("Expected correlation_id %s, got %s", firstSignalID, secondSignal.CorrelationID)
	}

	t.Logf("✅ Correlation verified: Signal %s correlates to Signal %s", secondSignalID, firstSignalID)

	// Test environment is ready - pause here as requested
	// Test helper functions
	t.Log("Testing helper functions...")

	// Test validateCorrelationID helper
	isValid := validateCorrelationID(t, ctx, queries, firstSignalID, testISN.Slug)
	if !isValid {
		t.Errorf("Expected first signal ID to be valid correlation ID, got false")
	}

	// Test getSignalByLocalRef helper
	retrievedSignal := getSignalByLocalRef(t, ctx, queries, ownerAccount.ID, signalType.Slug, signalType.SemVer, "correlation-test-signal-002")
	if retrievedSignal.ID != secondSignalID {
		t.Errorf("Expected retrieved signal ID %s, got %s", secondSignalID, retrievedSignal.ID)
	}

	// Test verifyCorrelation helper
	verifyCorrelation(t, ctx, queries, ownerAccount.ID, signalType.Slug, signalType.SemVer, "correlation-test-signal-002", firstSignalID)

	t.Log("✅ Helper functions working correctly")

	t.Log("🎯 Test environment setup complete and ready for correlation ID testing!")
	t.Log("   Available test data:")
	t.Log("   - Owner Account:", ownerAccount.ID)
	t.Log("   - ISN:", testISN.Slug)
	t.Log("   - Signal Type:", signalType.Slug+"/"+signalType.SemVer)
	t.Log("   - First Signal ID:", firstSignalID)
	t.Log("   - Second Signal ID (correlated):", secondSignalID)
}

// Helper function to create a signal with correlation ID using the database directly
func createCorrelatedSignal(t *testing.T, ctx context.Context, queries *database.Queries,
	accountID uuid.UUID, localRef string, correlationID uuid.UUID,
	signalTypeSlug, semVer string) uuid.UUID {

	signalID, err := queries.CreateOrUpdateSignalWithCorrelationID(ctx, database.CreateOrUpdateSignalWithCorrelationIDParams{
		AccountID:      accountID,
		LocalRef:       localRef,
		CorrelationID:  correlationID,
		SignalTypeSlug: signalTypeSlug,
		SemVer:         semVer,
	})
	if err != nil {
		t.Fatalf("Failed to create correlated signal %s: %v", localRef, err)
	}

	return signalID
}

// Helper function to create a basic signal without correlation
func createBasicSignal(t *testing.T, ctx context.Context, queries *database.Queries,
	accountID uuid.UUID, localRef string, signalTypeSlug, semVer string) uuid.UUID {

	signalID, err := queries.CreateSignal(ctx, database.CreateSignalParams{
		AccountID:      accountID,
		LocalRef:       localRef,
		SignalTypeSlug: signalTypeSlug,
		SemVer:         semVer,
	})
	if err != nil {
		t.Fatalf("Failed to create basic signal %s: %v", localRef, err)
	}

	return signalID
}

// Helper function to verify correlation relationship between two signals
func verifyCorrelation(t *testing.T, ctx context.Context, queries *database.Queries,
	accountID uuid.UUID, signalTypeSlug, semVer, localRef string, expectedCorrelationID uuid.UUID) {

	signal, err := queries.GetSignalByAccountAndLocalRef(ctx, database.GetSignalByAccountAndLocalRefParams{
		AccountID: accountID,
		Slug:      signalTypeSlug,
		SemVer:    semVer,
		LocalRef:  localRef,
	})
	if err != nil {
		t.Fatalf("Failed to get signal %s: %v", localRef, err)
	}

	if signal.CorrelationID != expectedCorrelationID {
		t.Errorf("Expected correlation_id %s, got %s for signal %s",
			expectedCorrelationID, signal.CorrelationID, localRef)
	}
}

// Helper function to get a signal by account and local ref
func getSignalByLocalRef(t *testing.T, ctx context.Context, queries *database.Queries,
	accountID uuid.UUID, signalTypeSlug, semVer, localRef string) database.GetSignalByAccountAndLocalRefRow {

	signal, err := queries.GetSignalByAccountAndLocalRef(ctx, database.GetSignalByAccountAndLocalRefParams{
		AccountID: accountID,
		Slug:      signalTypeSlug,
		SemVer:    semVer,
		LocalRef:  localRef,
	})
	if err != nil {
		t.Fatalf("Failed to get signal %s: %v", localRef, err)
	}

	return signal
}

// Helper function to validate that a correlation ID exists within the same ISN
func validateCorrelationID(t *testing.T, ctx context.Context, queries *database.Queries,
	correlationID uuid.UUID, isnSlug string) bool {

	isValid, err := queries.ValidateCorrelationID(ctx, database.ValidateCorrelationIDParams{
		CorrelationID: correlationID,
		IsnSlug:       isnSlug,
	})
	if err != nil {
		t.Fatalf("Failed to validate correlation ID %s: %v", correlationID, err)
	}

	return isValid
}
